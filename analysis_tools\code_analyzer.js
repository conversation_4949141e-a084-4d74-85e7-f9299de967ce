// JavaScript 代码分析工具
// 用于分析 bundle_beautified_v2.js 的结构和内容

const fs = require('fs');
const path = require('path');

class CodeAnalyzer {
    constructor(filePath) {
        this.filePath = filePath;
        this.content = '';
        this.classes = [];
        this.functions = [];
        this.constants = [];
        this.enums = [];
    }

    // 读取文件内容
    readFile() {
        try {
            this.content = fs.readFileSync(this.filePath, 'utf8');
            console.log(`文件读取成功，总行数: ${this.content.split('\n').length}`);
        } catch (error) {
            console.error('文件读取失败:', error);
        }
    }

    // 提取类定义
    extractClasses() {
        const classRegex = /class\s+(\w+)(?:\s+extends\s+(\w+))?\s*{/g;
        let match;
        
        while ((match = classRegex.exec(this.content)) !== null) {
            this.classes.push({
                name: match[1],
                extends: match[2] || null,
                position: match.index
            });
        }
        
        console.log(`找到 ${this.classes.length} 个类定义`);
        return this.classes;
    }

    // 提取函数定义
    extractFunctions() {
        const functionRegex = /(?:function\s+(\w+)|(\w+)\s*[:=]\s*function|(\w+)\s*\([^)]*\)\s*{)/g;
        let match;
        
        while ((match = functionRegex.exec(this.content)) !== null) {
            const name = match[1] || match[2] || match[3];
            if (name && name.length > 1) { // 过滤单字符变量
                this.functions.push({
                    name: name,
                    position: match.index
                });
            }
        }
        
        console.log(`找到 ${this.functions.length} 个函数定义`);
        return this.functions;
    }

    // 提取枚举定义
    extractEnums() {
        const enumRegex = /!function\s*\((\w+)\)\s*{[\s\S]*?}\s*\(\1\s*\|\|\s*\(\1\s*=\s*{\s*}\s*\)\)/g;
        let match;
        
        while ((match = enumRegex.exec(this.content)) !== null) {
            this.enums.push({
                name: match[1],
                position: match.index,
                content: match[0]
            });
        }
        
        console.log(`找到 ${this.enums.length} 个枚举定义`);
        return this.enums;
    }

    // 提取常量定义
    extractConstants() {
        const constantRegex = /(?:const|var|let)\s+(\w+)\s*=\s*([^;,\n]+)/g;
        let match;
        
        while ((match = constantRegex.exec(this.content)) !== null) {
            if (match[1].length > 1) { // 过滤单字符变量
                this.constants.push({
                    name: match[1],
                    value: match[2].trim(),
                    position: match.index
                });
            }
        }
        
        console.log(`找到 ${this.constants.length} 个常量定义`);
        return this.constants;
    }

    // 搜索特定模式
    searchPattern(pattern, description) {
        const regex = new RegExp(pattern, 'g');
        const matches = [];
        let match;
        
        while ((match = regex.exec(this.content)) !== null) {
            matches.push({
                match: match[0],
                position: match.index,
                line: this.getLineNumber(match.index)
            });
        }
        
        console.log(`${description}: 找到 ${matches.length} 个匹配`);
        return matches;
    }

    // 获取行号
    getLineNumber(position) {
        return this.content.substring(0, position).split('\n').length;
    }

    // 生成分析报告
    generateReport() {
        const report = {
            summary: {
                totalLines: this.content.split('\n').length,
                totalCharacters: this.content.length,
                classes: this.classes.length,
                functions: this.functions.length,
                enums: this.enums.length,
                constants: this.constants.length
            },
            classes: this.classes.slice(0, 20), // 前20个类
            functions: this.functions.slice(0, 50), // 前50个函数
            enums: this.enums.slice(0, 10), // 前10个枚举
            constants: this.constants.slice(0, 30), // 前30个常量
            gameSpecific: {
                events: this.searchPattern(/EVENT_\w+/g, '游戏事件'),
                weapons: this.searchPattern(/weapon|gun|rifle|pistol/gi, '武器相关'),
                network: this.searchPattern(/network|socket|connect/gi, '网络相关'),
                audio: this.searchPattern(/audio|sound|music/gi, '音频相关'),
                ui: this.searchPattern(/UI|interface|menu|button/gi, 'UI相关')
            }
        };
        
        return report;
    }

    // 保存报告到文件
    saveReport(outputPath) {
        const report = this.generateReport();
        fs.writeFileSync(outputPath, JSON.stringify(report, null, 2), 'utf8');
        console.log(`分析报告已保存到: ${outputPath}`);
    }

    // 运行完整分析
    analyze() {
        console.log('开始分析代码...');
        this.readFile();
        this.extractClasses();
        this.extractFunctions();
        this.extractEnums();
        this.extractConstants();
        
        const report = this.generateReport();
        console.log('\n=== 分析摘要 ===');
        console.log(`总行数: ${report.summary.totalLines}`);
        console.log(`总字符数: ${report.summary.totalCharacters}`);
        console.log(`类定义: ${report.summary.classes}`);
        console.log(`函数定义: ${report.summary.functions}`);
        console.log(`枚举定义: ${report.summary.enums}`);
        console.log(`常量定义: ${report.summary.constants}`);
        
        return report;
    }
}

// 使用示例
if (require.main === module) {
    const analyzer = new CodeAnalyzer('../bundle_beautified_v2.js');
    const report = analyzer.analyze();
    analyzer.saveReport('./analysis_report.json');
}

module.exports = CodeAnalyzer;
