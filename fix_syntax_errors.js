// JavaScript 语法错误修复脚本
// 用于修复 bundle_beautified_v2.js 的语法问题

const fs = require('fs');

class SyntaxFixer {
    constructor(filePath) {
        this.filePath = filePath;
        this.content = '';
        this.fixedContent = '';
        this.errors = [];
    }

    // 读取文件
    readFile() {
        try {
            this.content = fs.readFileSync(this.filePath, 'utf8');
            console.log(`文件读取成功，总行数: ${this.content.split('\n').length}`);
            return true;
        } catch (error) {
            console.error('文件读取失败:', error);
            return false;
        }
    }

    // 修复常见的语法错误
    fixCommonSyntaxErrors() {
        let fixed = this.content;

        // 1. 修复箭头函数语法
        fixed = fixed.replace(/\(\) = >/g, '() =>');

        // 2. 修复缺少分号的问题
        fixed = fixed.replace(/(\w+)\s*\(\s*\)\s*{/g, '$1() {');

        // 3. 修复 return 语句
        fixed = fixed.replace(/return\s*!/g, 'return !');
        fixed = fixed.replace(/return\s*\(/g, 'return (');

        // 4. 修复逗号表达式
        fixed = fixed.replace(/,(\w+)\s*=\s*/g, '; $1 = ');

        // 5. 修复函数参数
        fixed = fixed.replace(/\(\s*(\w+)\s*=\s*([^,)]+)\s*,/g, '($1 = $2, ');

        // 6. 修复对象属性
        fixed = fixed.replace(/(\w+)\s*:\s*function\s*\(/g, '$1: function(');

        // 7. 修复 class 语法
        fixed = fixed.replace(/class\s+(\w+)\s*{/g, 'class $1 {');

        // 8. 修复 for 循环
        fixed = fixed.replace(/for\s*\(\s*(\w+)\s*=\s*([^;]+)\s*;/g, 'for ($1 = $2;');

        // 9. 修复条件表达式
        fixed = fixed.replace(/if\s*\(\s*([^)]+)\s*\)\s*return\s*/g, 'if ($1) return ');

        // 10. 修复函数调用
        fixed = fixed.replace(/(\w+)\s*\.\s*(\w+)\s*\(/g, '$1.$2(');

        this.fixedContent = fixed;
        return fixed;
    }

    // 验证语法
    validateSyntax() {
        try {
            // 尝试解析 JavaScript
            new Function(this.fixedContent);
            console.log('✅ 语法验证通过');
            return true;
        } catch (error) {
            console.log('❌ 语法验证失败:', error.message);
            this.errors.push(error.message);
            return false;
        }
    }

    // 保存修复后的文件
    saveFixed(outputPath) {
        try {
            fs.writeFileSync(outputPath, this.fixedContent, 'utf8');
            console.log(`✅ 修复后的文件已保存到: ${outputPath}`);
            return true;
        } catch (error) {
            console.error('❌ 文件保存失败:', error);
            return false;
        }
    }

    // 创建可用于 JSNice 的版本
    createJSNiceVersion() {
        let jsNiceContent = this.fixedContent;

        // 移除可能导致问题的部分
        // 1. 移除 webpack 包装器
        jsNiceContent = jsNiceContent.replace(/^\(\(\) => \{/, '');
        jsNiceContent = jsNiceContent.replace(/\}\)\(\);?\s*$/, '');

        // 2. 简化复杂的表达式
        jsNiceContent = jsNiceContent.replace(/\?\?\?/g, 'null');
        jsNiceContent = jsNiceContent.replace(/\|\|\|/g, '||');

        // 3. 修复字符串中的特殊字符
        jsNiceContent = jsNiceContent.replace(/\\u([0-9a-fA-F]{4})/g, '\\u$1');

        // 4. 确保所有语句都有分号
        jsNiceContent = jsNiceContent.replace(/([^;{}\s])\s*\n/g, '$1;\n');

        return jsNiceContent;
    }

    // 运行完整修复流程
    fix() {
        console.log('🔧 开始修复语法错误...');
        
        if (!this.readFile()) {
            return false;
        }

        console.log('📝 应用语法修复...');
        this.fixCommonSyntaxErrors();

        console.log('🔍 验证语法...');
        const isValid = this.validateSyntax();

        // 保存修复后的版本
        this.saveFixed('bundle_fixed.js');

        // 创建 JSNice 专用版本
        console.log('🎯 创建 JSNice 专用版本...');
        const jsNiceVersion = this.createJSNiceVersion();
        fs.writeFileSync('bundle_for_jsnice.js', jsNiceVersion, 'utf8');
        console.log('✅ JSNice 版本已保存到: bundle_for_jsnice.js');

        return isValid;
    }
}

// 使用示例
if (require.main === module) {
    const fixer = new SyntaxFixer('./bundle_beautified_v2.js');
    const success = fixer.fix();
    
    if (success) {
        console.log('\n🎉 修复完成！下一步：');
        console.log('1. 使用 bundle_for_jsnice.js 上传到 JSNice');
        console.log('2. 如果还有问题，尝试分段处理');
    } else {
        console.log('\n⚠️ 自动修复未完全成功，需要手动处理');
    }
}

module.exports = SyntaxFixer;
