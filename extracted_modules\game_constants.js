// 游戏常量配置模块
// 从 bundle_beautified_v2.js 中提取的游戏配置

var GameConst;
!function (e) {
    // 版本信息
    e.GAME_VERSION = "3.51";
    e.RES_VERSION = "3.51.0";
    e.CORE_VERSION = "0";
    e.IOS_Code_VERSION = 2023071401;
    
    // 调试开关
    e.ENABLE_NET_LOG = false;
    e.ENABLE_GAME_LOG = false;
    e.ENABLE_KBE_LOG = false;
    e.IS_SHOW_GM_BUTTON = true;
    e.IS_Enable_GameRecord_LOG = true;
    
    // CDN配置
    e.ONLINE_CDN_URL = "https://cdnqhzs.51aiwan.com/";
    e.ONLINE_TEST_CDN_URL = "https://cdnqhzstest.51aiwan.com/";
    e.ONLINE_CDN_URL_VPN = "https://qhzs-1252368878.file.myqcloud.com/";
    e.ONLINE_CDN_URL_DISCORD = "https://1345932571887931454.discordsays.com/.proxy/DiscordTest/";
    e.REMOTE_CDN_URL = "http://************:9002/";
    e.LOCAL_CDN_URL = "http://************:9002/";
    
    // 平台和服务器配置
    e.platfromType = 1011;
    e.SERVER_TYPE = 0;
    e.GAME_STATE = 3;
    
    // 功能开关
    e.IS_SHOW_Mesh_lineSprite3D = false;
    e.TEST_AD_MATRIX = true;
    e.pretendTest = false;
    e.TEST_SKIP_SERVER_SELECT = false;
    e.DownloadResTest = false;
    e.MouseAndKeyboard = false;
    e.canBeDistanceLogin = false;
    e.isDistanceLogin = false;
    e.isDistanceLoginTipTimes = false;
    e.isServerConnectRefuse = false;
    e.isTestUseFour3Contrl = false;
    
    // 测试服务器配置
    e.LOCAL_TEST_SERVER_DOMAIN = "************";
    e.TEST_API_ADRESS = "https://service-bjq4fvdv-1258588897.sh.apigw.tencentcs.com/test/cmge_test_2";
    
    // 工具函数
    e.attachToWindow = function (variableName, value) {
        if (e.ENABLE_GAME_LOG) {
            window[variableName] = value;
        }
    };
}(GameConst || (GameConst = {}));

// 角色状态定义
var CharacterState;
!function (e) {
    e[e.None = 0] = "None";
    e[e.Idle = 1] = "Idle";
    e[e.Walk = 2] = "Walk";
    e[e.Run = 3] = "Run";
    e[e.Jump = 4] = "Jump";
    e[e.Fall = 5] = "Fall";
    e[e.Land = 6] = "Land";
    e[e.Crouch = 7] = "Crouch";
    e[e.CrouchWalk = 8] = "CrouchWalk";
    e[e.Prone = 9] = "Prone";
    e[e.ProneWalk = 10] = "ProneWalk";
    e[e.Die = 11] = "Die";
}(CharacterState || (CharacterState = {}));

// 武器类型定义
var eWeaponType;
!function (e) {
    e[e.MachinePistol = 1] = "MachinePistol";      // 冲锋枪
    e[e.Assault_Rifle = 2] = "Assault_Rifle";      // 突击步枪
    e[e.Sniper_Rifle = 3] = "Sniper_Rifle";       // 狙击步枪
    e[e.Shotgun = 4] = "Shotgun";                  // 霰弹枪
    e[e.LightMachineGun = 5] = "LightMachineGun";  // 轻机枪
    e[e.Pistol = 6] = "Pistol";                   // 手枪
    e[e.Knife = 7] = "Knife";                     // 刀具
    e[e.Grenade = 8] = "Grenade";                 // 手榴弹
}(eWeaponType || (eWeaponType = {}));

// 武器状态定义
var eWeaponState;
!function (e) {
    e[e.Idle = 0] = "Idle";
    e[e.Firing = 1] = "Firing";
    e[e.Reloading = 2] = "Reloading";
    e[e.Switching = 3] = "Switching";
    e[e.Aiming = 4] = "Aiming";
}(eWeaponState || (eWeaponState = {}));

// 团队定义
var eTeam;
!function (e) {
    e[e.NoTeam = -1] = "NoTeam";
    e[e.Blue = 0] = "Blue";
    e[e.Red = 1] = "Red";
    e[e.Green = 2] = "Green";
    e[e.Yellow = 3] = "Yellow";
}(eTeam || (eTeam = {}));

// 战斗类型定义
var eBattleType;
!function (e) {
    e[e.AISUB_Mode = 0] = "AISUB_Mode";  // AI替代模式
    e[e.PvP_Mode = 1] = "PvP_Mode";      // 玩家对战模式
    e[e.PvE_Mode = 2] = "PvE_Mode";      // 玩家对环境模式
    e[e.Training_Mode = 3] = "Training_Mode"; // 训练模式
}(eBattleType || (eBattleType = {}));

export { 
    GameConst, 
    CharacterState, 
    eWeaponType, 
    eWeaponState, 
    eTeam, 
    eBattleType 
};
