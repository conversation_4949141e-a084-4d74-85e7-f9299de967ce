// 游戏事件定义模块
// 从 bundle_beautified_v2.js 中提取的事件系统

var EventDef;
!function (e) {
    // 基础事件
    e[e.LANGUAGE_CHANGE = 0] = "LANGUAGE_CHANGE";
    e[e.OPEN_SPECIAL_BOX = 1] = "OPEN_SPECIAL_BOX";
    e[e.EVENT_OLDPLAYERSIGNIN_UPDATE = 2] = "EVENT_OLDPLAYERSIGNIN_UPDATE";
    e[e.EVENT_COMMONSIGNIN_UPDATE = 3] = "EVENT_COMMONSIGNIN_UPDATE";
    e[e.EVENT_SPECIALACTIVITY_UPDATE = 4] = "EVENT_SPECIALACTIVITY_UPDATE";
    e[e.EVENT_UPDATE_OPTIONALITEMBOX = 5] = "EVENT_UPDATE_OPTIONALITEMBOX";
    e[e.EVENT_LUCKYBOXDRAW_FINISHED = 6] = "EVENT_LUCKYBOXDRAW_FINISHED";
    e[e.EVENT_MAOSHANDRAW_FINISHED = 7] = "EVENT_MAOSHANDRAW_FINISHED";
    e[e.EVENT_BREAK_LUCKYBOX_ROTATE = 8] = "EVENT_BREAK_LUCKYBOX_ROTATE";
    
    // 时间和服务器事件
    e[e.EVENT_CLIENT_GET_TIME_SUCCESS = 9] = "EVENT_CLIENT_GET_TIME_SUCCESS";
    e[e.EVENT_ON_SERVER_TIME = 10] = "EVENT_ON_SERVER_TIME";
    e[e.EVENT_ON_CONNECT_SERVER = 11] = "EVENT_ON_CONNECT_SERVER";
    
    // UI事件
    e[e.EVENT_ON_UI_CHANGE = 12] = "EVENT_ON_UI_CHANGE";
    e[e.EVENT_ON_Base_UI_CHANGE_ResClear = 13] = "EVENT_ON_Base_UI_CHANGE_ResClear";
    e[e.EVENT_ON_BACKGROUND_CHANGE = 14] = "EVENT_ON_BACKGROUND_CHANGE";
    
    // 射击事件
    e[e.EVET_FIRE = 15] = "EVET_FIRE";
    e[e.EVENT_START_FIRE = 16] = "EVENT_START_FIRE";
    e[e.EVENT_START_FIRE_AUTO = 17] = "EVENT_START_FIRE_AUTO";
    e[e.EVENT_START_BAYONET_FIRE = 18] = "EVENT_START_BAYONET_FIRE";
    e[e.EVENT_STOP_FIRE = 19] = "EVENT_STOP_FIRE";
    e[e.EVENT_STOP_FIRE_AUTO = 20] = "EVENT_STOP_FIRE_AUTO";
    
    // 能量储存事件
    e[e.EVENT_START_ENERGY_STORAGE = 21] = "EVENT_START_ENERGY_STORAGE";
    e[e.EVENT_START_ENERGY_STORAGE_SUCCESS = 22] = "EVENT_START_ENERGY_STORAGE_SUCCESS";
    e[e.EVENT_STOP_ENERGY_STORAGE = 23] = "EVENT_STOP_ENERGY_STORAGE";
    
    // 触摸和控制事件
    e[e.EVENT_RIGHT_SCREEN_TOUCH = 24] = "EVENT_RIGHT_SCREEN_TOUCH";
    e[e.EVENT_RIGHT_SCREEN_TOUCH_DOWN = 25] = "EVENT_RIGHT_SCREEN_TOUCH_DOWN";
    e[e.EVENT_RIGHT_SCREEN_TOUCH_UP = 26] = "EVENT_RIGHT_SCREEN_TOUCH_UP";
    e[e.EVENT_JOY_STICK_TOUCH = 27] = "EVENT_JOY_STICK_TOUCH";
    
    // 更多事件定义...
    // 注：原文件中包含约200个事件定义
}(EventDef || (EventDef = {}));

// 平台类型定义
var ePlatform;
!function (e) {
    e[e.PlatformTest = 9999] = "PlatformTest";
    e[e.PlatformWX = 1002] = "PlatformWX";  // 微信平台
    e[e.PlatformIOS = 1011] = "PlatformIOS"; // iOS平台
    e[e.Platform4399 = 1003] = "Platform4399"; // 4399平台
    // 更多平台定义...
}(ePlatform || (ePlatform = {}));

// 游戏状态定义
var eGameState;
!function (e) {
    e[e.Test = 0] = "Test";
    e[e.LocalTest = 1] = "LocalTest";
    e[e.Online = 3] = "Online";
}(eGameState || (eGameState = {}));

// 服务器类型定义
var eServerType;
!function (e) {
    e[e.Online = 0] = "Online";
    e[e.Test = 1] = "Test";
}(eServerType || (eServerType = {}));

export { EventDef, ePlatform, eGameState, eServerType };
